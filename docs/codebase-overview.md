# Codebase Overview

This file provides a summary of the portfolio project's codebase to aid in understanding and working with AI coding agents like Cline or Cursor.

## Project Structure

- **src/**: Contains the main source code for the React/TypeScript portfolio site.
  - **components/**: Reusable UI components (e.g., <PERSON><PERSON>, <PERSON>er, Hero, Projects).
  - **data/**: Static data files (e.g., portfolio.ts for project and skill data).
  - **types/**: TypeScript type definitions.
  - **utils/**: Utility functions (e.g., className merging with cn.ts).

## Tech Stack

- **React**: Frontend library for building user interfaces.
- **TypeScript**: Static typing for JavaScript.
- **Tailwind CSS**: Utility-first CSS framework for styling.
- **Vite**: Build tool and development server for fast performance.

## Key Components

- **App.tsx**: Main entry point for the application.
- **Hero.tsx**, **Projects.tsx**, **Skills.tsx**: Core sections of the portfolio.

## Notes

- Add any specific details or quirks about the codebase that agents should be aware of.
