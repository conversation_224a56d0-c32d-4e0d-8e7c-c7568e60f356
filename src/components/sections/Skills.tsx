import React from 'react';
import { motion } from 'framer-motion';
import { Card } from '../ui/Card';
import { skills } from '../../data/portfolio';
import { Skill } from '../../types/portfolio';

export const Skills: React.FC = () => {
  const categories = {
    frontend: { name: 'Frontend', color: 'from-blue-500 to-cyan-500' },
    backend: { name: 'Backend', color: 'from-green-500 to-emerald-500' },
    database: { name: 'Database', color: 'from-purple-500 to-pink-500' },
    tools: { name: 'Tools', color: 'from-orange-500 to-red-500' },
    cloud: { name: 'Cloud', color: 'from-yellow-500 to-orange-500' }
  };

  const skillsByCategory = Object.keys(categories).reduce((acc, category) => {
    acc[category] = skills.filter(skill => skill.category === category);
    return acc;
  }, {} as Record<string, Skill[]>);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  };

  const progressVariants = {
    hidden: { width: 0 },
    visible: (level: number) => ({
      width: `${level * 10}%`,
      transition: { duration: 1, delay: 0.5, ease: 'easeOut' }
    })
  };

  return (
    <section id="skills" className="py-20 bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="text-center mb-16"
        >
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            Skills & Expertise
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-600 mx-auto mb-8"></div>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            A comprehensive overview of my technical skills and proficiency levels 
            across different domains of software development.
          </p>
        </motion.div>

        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
          className="space-y-12"
        >
          {Object.entries(skillsByCategory).map(([categoryKey, categorySkills]) => {
            const category = categories[categoryKey as keyof typeof categories];
            
            return (
              <motion.div key={categoryKey} variants={itemVariants}>
                <Card glass>
                  <div className="mb-6">
                    <div className="flex items-center space-x-3 mb-4">
                      <div className={`w-8 h-8 bg-gradient-to-r ${category.color} rounded-lg`}></div>
                      <h3 className="text-2xl font-bold text-white">{category.name}</h3>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {categorySkills.map((skill) => (
                      <motion.div
                        key={skill.id}
                        initial={{ opacity: 0, x: -20 }}
                        whileInView={{ opacity: 1, x: 0 }}
                        viewport={{ once: true }}
                        transition={{ duration: 0.5 }}
                        className="space-y-3"
                      >
                        <div className="flex justify-between items-center">
                          <span className="text-white font-medium">{skill.name}</span>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-gray-400">
                              {skill.yearsExperience}y
                            </span>
                            <span className="text-sm font-semibold text-blue-400">
                              {skill.proficiencyLevel * 10}%
                            </span>
                          </div>
                        </div>
                        
                        <div className="relative">
                          <div className="w-full bg-gray-700 rounded-full h-2">
                            <motion.div
                              variants={progressVariants}
                              initial="hidden"
                              whileInView="visible"
                              viewport={{ once: true }}
                              custom={skill.proficiencyLevel}
                              className={`h-2 bg-gradient-to-r ${category.color} rounded-full relative overflow-hidden`}
                            >
                              <div className="absolute inset-0 bg-white/20 animate-pulse"></div>
                            </motion.div>
                          </div>
                        </div>
                      </motion.div>
                    ))}
                  </div>
                </Card>
              </motion.div>
            );
          })}
        </motion.div>

        {/* Skills Summary */}
        <motion.div
          initial={{ opacity: 0, y: 30 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          className="mt-16"
        >
          <Card glass className="text-center">
            <h3 className="text-2xl font-bold text-white mb-6">Professional Highlights</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              <div>
                <div className="text-3xl font-bold text-blue-400 mb-2">12+</div>
                <div className="text-gray-300">Technologies Mastered</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-purple-400 mb-2">3.5</div>
                <div className="text-gray-300">Years Average Experience</div>
              </div>
              <div>
                <div className="text-3xl font-bold text-green-400 mb-2">85%</div>
                <div className="text-gray-300">Average Proficiency</div>
              </div>
            </div>
          </Card>
        </motion.div>
      </div>
    </section>
  );
};