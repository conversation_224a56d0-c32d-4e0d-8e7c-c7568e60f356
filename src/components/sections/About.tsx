import React from 'react';
import { motion } from 'framer-motion';
import { Card } from '../ui/Card';
import { Code2, Database, Server, Palette } from 'lucide-react';

export const About: React.FC = () => {
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 30 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.6 }
    }
  };

  const specialties = [
    {
      icon: Code2,
      title: 'Frontend Development',
      description: 'React, TypeScript, Next.js with modern UI/UX design principles and responsive layouts.',
      color: 'from-blue-500 to-cyan-500'
    },
    {
      icon: Server,
      title: 'Backend Development',
      description: 'Node.js, Express.js, API design, microservices architecture, and cloud deployment.',
      color: 'from-green-500 to-emerald-500'
    },
    {
      icon: Database,
      title: 'Database Design',
      description: 'PostgreSQL, MongoDB, data modeling, optimization, and database performance tuning.',
      color: 'from-purple-500 to-pink-500'
    },
    {
      icon: Palette,
      title: 'UI/UX Design',
      description: 'User-centered design, prototyping, accessibility, and modern design systems.',
      color: 'from-orange-500 to-red-500'
    }
  ];

  return (
    <section id="about" className="py-20 bg-gray-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          whileInView="visible"
          viewport={{ once: true, margin: "-100px" }}
        >
          <motion.div variants={itemVariants} className="text-center mb-16">
            <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
              About Me
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-600 mx-auto mb-8"></div>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto leading-relaxed">
              A passionate full-stack developer with 3+ years of experience building 
              scalable web applications. I love turning complex problems into simple, 
              beautiful, and intuitive solutions.
            </p>
          </motion.div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-16">
            {specialties.map((specialty, index) => (
              <motion.div key={specialty.title} variants={itemVariants}>
                <Card hover glass className="h-full text-center">
                  <div className={`w-16 h-16 bg-gradient-to-r ${specialty.color} rounded-full flex items-center justify-center mx-auto mb-4`}>
                    <specialty.icon className="w-8 h-8 text-white" />
                  </div>
                  <h3 className="text-xl font-semibold text-white mb-3">
                    {specialty.title}
                  </h3>
                  <p className="text-gray-300 text-sm leading-relaxed">
                    {specialty.description}
                  </p>
                </Card>
              </motion.div>
            ))}
          </div>

          <motion.div variants={itemVariants}>
            <Card glass className="max-w-4xl mx-auto">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 items-center">
                <div>
                  <h3 className="text-2xl font-bold text-white mb-4">
                    My Development Journey
                  </h3>
                  <div className="space-y-4 text-gray-300">
                    <p>
                      Started my coding journey in 2021 with JavaScript and quickly fell in love 
                      with React. Since then, I've been continuously learning and adapting to 
                      new technologies in the ever-evolving web development landscape.
                    </p>
                    <p>
                      I believe in writing clean, maintainable code and creating user experiences 
                      that not only look great but also perform exceptionally well. Every project 
                      is an opportunity to learn something new and push the boundaries of what's possible.
                    </p>
                    <p>
                      When I'm not coding, you'll find me exploring new technologies, contributing 
                      to open-source projects, or sharing my knowledge through technical writing 
                      and mentoring fellow developers.
                    </p>
                  </div>
                </div>
                
                <div className="space-y-6">
                  <div>
                    <h4 className="text-lg font-semibold text-white mb-3">Key Stats</h4>
                    <div className="grid grid-cols-2 gap-4">
                      <div className="text-center">
                        <div className="text-3xl font-bold text-blue-400">50+</div>
                        <div className="text-sm text-gray-400">Projects Completed</div>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold text-purple-400">3+</div>
                        <div className="text-sm text-gray-400">Years Experience</div>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold text-green-400">15+</div>
                        <div className="text-sm text-gray-400">Technologies</div>
                      </div>
                      <div className="text-center">
                        <div className="text-3xl font-bold text-orange-400">100%</div>
                        <div className="text-sm text-gray-400">Client Satisfaction</div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </Card>
          </motion.div>
        </motion.div>
      </div>
    </section>
  );
};