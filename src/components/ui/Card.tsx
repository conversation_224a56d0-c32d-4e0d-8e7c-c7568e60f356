import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '../../utils/cn';

interface CardProps {
  children: React.ReactNode;
  className?: string;
  hover?: boolean;
  glass?: boolean;
}

export const Card: React.FC<CardProps> = ({ 
  children, 
  className, 
  hover = false,
  glass = false 
}) => {
  return (
    <motion.div
      whileHover={hover ? { y: -5, scale: 1.02 } : {}}
      transition={{ duration: 0.2 }}
      className={cn(
        'rounded-xl p-6 transition-all duration-300',
        glass 
          ? 'bg-white/10 backdrop-blur-md border border-white/20 shadow-lg'
          : 'bg-gray-800 border border-gray-700 shadow-lg',
        hover && 'hover:shadow-2xl hover:shadow-blue-500/10',
        className
      )}
    >
      {children}
    </motion.div>
  );
};