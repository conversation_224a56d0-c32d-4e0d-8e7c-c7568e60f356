import { Project, Skill, Experience } from '../types/portfolio';

export const projects: Project[] = [
  {
    id: '1',
    title: 'NestCura - Healthcare Management Platform',
    description: 'Comprehensive healthcare management system with patient tracking, appointment scheduling, and medical records management.',
    longDescription: 'A full-stack healthcare platform built with React, Node.js, and PostgreSQL. Features include real-time appointment scheduling, secure patient data management, doctor-patient communication portal, and advanced analytics dashboard for healthcare providers.',
    technologyStack: ['React', 'Node.js', 'PostgreSQL', 'TypeScript', 'Express.js', 'Socket.io', 'JWT'],
    githubUrl: 'https://github.com/yourusername/nestcura',
    liveDemoUrl: 'https://nestcura-demo.com',
    imageUrl: 'https://images.pexels.com/photos/4386466/pexels-photo-4386466.jpeg?auto=compress&cs=tinysrgb&w=800',
    projectType: 'web_app',
    status: 'completed',
    featured: true,
    createdAt: '2024-01-15'
  },
  {
    id: '2',
    title: 'YourWay - Travel Planning Assistant',
    description: 'AI-powered travel planning platform with personalized itinerary generation and booking management.',
    longDescription: 'An intelligent travel companion that leverages AI to create personalized travel itineraries. Built with modern React architecture and integrated with multiple travel APIs for real-time pricing and availability.',
    technologyStack: ['React', 'Next.js', 'OpenAI API', 'Prisma', 'MongoDB', 'Tailwind CSS', 'Vercel'],
    githubUrl: 'https://github.com/yourusername/yourway',
    liveDemoUrl: 'https://yourway-travel.com',
    imageUrl: 'https://images.pexels.com/photos/1430676/pexels-photo-1430676.jpeg?auto=compress&cs=tinysrgb&w=800',
    projectType: 'web_app',
    status: 'completed',
    featured: true,
    createdAt: '2024-02-20'
  },
  {
    id: '3',
    title: 'Advanced Table Builder',
    description: 'Dynamic table component library with sorting, filtering, pagination, and export capabilities.',
    longDescription: 'A comprehensive table builder component library designed for enterprise applications. Features advanced filtering, custom cell renderers, virtual scrolling for large datasets, and extensive customization options.',
    technologyStack: ['React', 'TypeScript', 'Storybook', 'Rollup', 'Jest', 'React Testing Library'],
    githubUrl: 'https://github.com/yourusername/table-builder',
    liveDemoUrl: 'https://table-builder-demo.com',
    imageUrl: 'https://images.pexels.com/photos/590016/pexels-photo-590016.jpeg?auto=compress&cs=tinysrgb&w=800',
    projectType: 'component_library',
    status: 'completed',
    featured: true,
    createdAt: '2024-03-10'
  },
  {
    id: '4',
    title: 'Canvas Builder System',
    description: 'Drag-and-drop visual editor for creating interactive dashboards and presentations.',
    longDescription: 'A powerful visual editor that allows users to create interactive dashboards through an intuitive drag-and-drop interface. Built with React and Canvas API for smooth performance and real-time collaboration features.',
    technologyStack: ['React', 'Canvas API', 'WebRTC', 'Node.js', 'Socket.io', 'Redis', 'Docker'],
    githubUrl: 'https://github.com/yourusername/canvas-builder',
    liveDemoUrl: 'https://canvas-builder-demo.com',
    imageUrl: 'https://images.pexels.com/photos/196644/pexels-photo-196644.jpeg?auto=compress&cs=tinysrgb&w=800',
    projectType: 'tool',
    status: 'in_progress',
    featured: true,
    createdAt: '2024-04-05'
  },
  {
    id: '5',
    title: 'E-Commerce API Gateway',
    description: 'Microservices-based API gateway for scalable e-commerce platforms.',
    longDescription: 'A robust API gateway designed for e-commerce platforms handling high traffic. Implements rate limiting, authentication, request routing, and monitoring across multiple microservices.',
    technologyStack: ['Node.js', 'Express.js', 'Redis', 'PostgreSQL', 'Docker', 'Kubernetes', 'Nginx'],
    githubUrl: 'https://github.com/yourusername/ecommerce-api',
    imageUrl: 'https://images.pexels.com/photos/163064/play-stone-network-networked-interactive-163064.jpeg?auto=compress&cs=tinysrgb&w=800',
    projectType: 'api',
    status: 'completed',
    featured: false,
    createdAt: '2024-05-12'
  },
  {
    id: '6',
    title: 'Real-time Chat Application',
    description: 'Scalable chat application with real-time messaging, file sharing, and video calling.',
    longDescription: 'A feature-rich chat application supporting real-time messaging, file sharing, video calls, and group conversations. Built with modern web technologies for optimal performance and user experience.',
    technologyStack: ['React', 'Socket.io', 'WebRTC', 'Node.js', 'MongoDB', 'AWS S3', 'TypeScript'],
    githubUrl: 'https://github.com/yourusername/chat-app',
    liveDemoUrl: 'https://chat-app-demo.com',
    imageUrl: 'https://images.pexels.com/photos/8849295/pexels-photo-8849295.jpeg?auto=compress&cs=tinysrgb&w=800',
    projectType: 'web_app',
    status: 'completed',
    featured: false,
    createdAt: '2024-06-18'
  }
];

export const skills: Skill[] = [
  { id: '1', name: 'React', category: 'frontend', proficiencyLevel: 9, yearsExperience: 3.5, iconName: 'react' },
  { id: '2', name: 'TypeScript', category: 'frontend', proficiencyLevel: 9, yearsExperience: 3, iconName: 'typescript' },
  { id: '3', name: 'Next.js', category: 'frontend', proficiencyLevel: 8, yearsExperience: 2.5, iconName: 'nextjs' },
  { id: '4', name: 'Node.js', category: 'backend', proficiencyLevel: 9, yearsExperience: 3.5, iconName: 'nodejs' },
  { id: '5', name: 'Express.js', category: 'backend', proficiencyLevel: 8, yearsExperience: 3, iconName: 'express' },
  { id: '6', name: 'PostgreSQL', category: 'database', proficiencyLevel: 8, yearsExperience: 3, iconName: 'postgresql' },
  { id: '7', name: 'MongoDB', category: 'database', proficiencyLevel: 7, yearsExperience: 2.5, iconName: 'mongodb' },
  { id: '8', name: 'Tailwind CSS', category: 'frontend', proficiencyLevel: 9, yearsExperience: 2, iconName: 'tailwind' },
  { id: '9', name: 'Docker', category: 'tools', proficiencyLevel: 7, yearsExperience: 2, iconName: 'docker' },
  { id: '10', name: 'AWS', category: 'cloud', proficiencyLevel: 7, yearsExperience: 2.5, iconName: 'aws' },
  { id: '11', name: 'Git', category: 'tools', proficiencyLevel: 9, yearsExperience: 4, iconName: 'git' },
  { id: '12', name: 'Jest', category: 'tools', proficiencyLevel: 8, yearsExperience: 2.5, iconName: 'jest' }
];

export const experiences: Experience[] = [
  {
    id: '1',
    company: 'TechCorp Solutions',
    position: 'Senior Full-Stack Developer',
    duration: '2023 - Present',
    description: 'Lead development of enterprise web applications using modern MERN stack technologies.',
    technologies: ['React', 'Node.js', 'PostgreSQL', 'TypeScript', 'AWS'],
    achievements: [
      'Improved application performance by 40% through code optimization',
      'Led a team of 4 developers on critical project deliveries',
      'Implemented CI/CD pipelines reducing deployment time by 60%'
    ]
  },
  {
    id: '2',
    company: 'StartupInnovate',
    position: 'Full-Stack Developer',
    duration: '2022 - 2023',
    description: 'Developed MVP products and scalable web applications for startup clients.',
    technologies: ['React', 'Express.js', 'MongoDB', 'Docker', 'Digital Ocean'],
    achievements: [
      'Built 5+ production applications from concept to deployment',
      'Reduced server costs by 30% through optimization strategies',
      'Mentored junior developers and established coding standards'
    ]
  },
  {
    id: '3',
    company: 'WebDev Agency',
    position: 'Frontend Developer',
    duration: '2021 - 2022',
    description: 'Created responsive, user-friendly interfaces for diverse client projects.',
    technologies: ['React', 'JavaScript', 'Sass', 'WordPress', 'Figma'],
    achievements: [
      'Delivered 20+ client projects with 100% satisfaction rate',
      'Improved website loading speeds by average of 45%',
      'Established component library used across multiple projects'
    ]
  }
];