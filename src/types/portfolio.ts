export interface Project {
  id: string;
  title: string;
  description: string;
  longDescription: string;
  technologyStack: string[];
  githubUrl?: string;
  liveDemoUrl?: string;
  blogPostUrl?: string;
  imageUrl: string;
  projectType: 'web_app' | 'mobile_app' | 'api' | 'tool' | 'component_library';
  status: 'completed' | 'in_progress' | 'planning';
  featured: boolean;
  createdAt: string;
}

export interface Skill {
  id: string;
  name: string;
  category: 'frontend' | 'backend' | 'database' | 'tools' | 'cloud';
  proficiencyLevel: number;
  yearsExperience: number;
  iconName: string;
}

export interface Experience {
  id: string;
  company: string;
  position: string;
  duration: string;
  description: string;
  technologies: string[];
  achievements: string[];
}

export interface ContactForm {
  name: string;
  email: string;
  subject: string;
  message: string;
}