# Project Rules

This file outlines specific rules and guidelines for the portfolio project to ensure consistency when working with AI coding agents like Cline or Cursor.

## Coding Standards

- **Naming Conventions**: [e.g., Use camelCase for variables, PascalCase for components]
- **File Structure**: [e.g., Keep components in src/components/, data in src/data/]
- **Formatting**: [e.g., Use 2-space indentation, enforced by ESLint]

## Component Guidelines

- **Reusable Components**: Ensure components are modular and reusable where possible.
- **Props**: Document props clearly for each component.

## Other Rules

- Add any additional project-specific rules or preferences for AI agents to follow.
